local websocket = require('websocket')
local Emitter = require('core').Emitter
local json = require('json')

local Bot = {}
Bot.__index = Bot

function Bot:new(config)
    local self = setmetatable({}, Bot)
    self.config = config
    self.server = nil
    self.clients = {}
    self.events = Emitter:new()
    self.plugins = {}
    return self
end

function Bot:start()
    print("启动Bot WebSocket服务器，监听端口: " .. self.config.port)

    self.server = websocket.server.new()

    -- 监听客户端连接
    self.server:on('connect', function(client)
        print("OneBot客户端已连接，ID: " .. (client.id or "unknown"))
        self.clients[client.id or #self.clients + 1] = client

        -- 发送欢迎消息
        client:send(json.stringify({
            status = "connected",
            message = "Bot服务器连接成功"
        }))
    end)

    -- 监听客户端消息
    self.server:on('data', function(client, data)
        self:handleMessage(client, data)
    end)

    -- 监听客户端断开
    self.server:on('disconnect', function(client)
        print("OneBot客户端已断开连接，ID: " .. (client.id or "unknown"))
        if client.id then
            self.clients[client.id] = nil
        end
    end)

    -- 启动服务器
    self.server:listen(self.config.port, self.config.host or "127.0.0.1")
    print("Bot WebSocket服务器已启动在 ws://" .. (self.config.host or "127.0.0.1") .. ":" .. self.config.port)
end

function Bot:handleMessage(client, data)
    local success, msg = pcall(json.parse, data)
    if success and msg then
        -- 存储客户端引用以便回复
        msg._client = client
        self.events:emit(msg.post_type or 'message', msg)
    else
        print("解析消息失败: " .. tostring(data))
    end
end

function Bot:sendMessage(params, targetClient)
    local data = {
        action = "send_msg",
        params = params
    }

    local message = json.stringify(data)

    if targetClient then
        -- 发送给指定客户端
        targetClient:send(message)
    else
        -- 广播给所有客户端
        for _, client in pairs(self.clients) do
            client:send(message)
        end
    end
end

function Bot:sendToClient(clientId, data)
    local client = self.clients[clientId]
    if client then
        client:send(json.stringify(data))
    end
end

return Bot
